package trading.exchanges.bitget.socket

import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonContentPolymorphicSerializer
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import okhttp3.WebSocket
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import trading.events.*
import trading.exchanges.bitget.*
import trading.exchanges.socket.PrivateSocketListener
import trading.models.ExExecution
import trading.models.ExLiquidation
import trading.models.ExPosition
import trading.models.FeeAsset.Companion.toFeeAsset
import trading.models.OrdStatus
import trading.models.Side
import trading.models.SymbolEx
import trading.trading.holders.PositionsHolder
import trading.utils.MaxSizeHashMap
import java.time.Instant

class BitgetPrivateSocketListener(
    private val eventPublisher: EventPublisher,
    private val ordersCache: BitgetOrdersCache,
    private val positionsHolder: PositionsHolder
) : PrivateSocketListener() {

    private val format = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    // SymbolEx -> totalFunding from prev message
    private val fundings = MaxSizeHashMap<SymbolEx, Double>(10)

    override val log: Logger = LoggerFactory.getLogger(this.javaClass)

    override fun internalOnMessage(text: String, socket: WebSocket) {
        val receivedAt = System.nanoTime()
        try {
            val resp = format.decodeFromString<BaseWebSocketResponse>(text)
            when (resp) {
                is FillResponse -> {
                    val fills = resp.data.groupBy { it.tradeSide }

                    // handle trades
                    (fills[BitgetTradeType.BuySingle.code].orEmpty() + fills[BitgetTradeType.SellSingle.code].orEmpty())
                        .takeIf { it.isNotEmpty() }
                        ?.let { trades ->
                            eventPublisher.publishBlocking(
                                ExExecutionEvent(
                                    Integrity.New,
                                    receivedAt,
                                    trades.map { it.toExExecution() }
                                )
                            )
                        }

                    // handle liquidations
                    (fills[BitgetTradeType.ReduceBuySingle.code].orEmpty() + fills[BitgetTradeType.ReduceSellSingle.code].orEmpty() + fills[BitgetTradeType.BurstBuySingle.code].orEmpty() + fills[BitgetTradeType.BurstSellSingle.code].orEmpty())
                        .takeIf { it.isNotEmpty() }
                        ?.let { liqs ->
                            eventPublisher.publishBlocking(
                                ExLiquidationEvent(
                                    Integrity.New,
                                    liqs.map { it.toExLiquidation() }
                                )
                            )
                            log.info("Liquidation ws message: $text")
                        }
                }

                is OrdersResponse -> {
                    resp.data.forEach { ordersCache.store(it) }
                }

                is PositionsResponse -> {
                    // Bitget always sends us the whole info about opened positions, even where there is no positions.
                    // We should even store empty positions message within [PositionsHolder] to clear them and not
                    // to come across disproportion.
                    resp.data.map { it.toExPosition() }.let {
                        positionsHolder.store(ProductType.parse(resp.arg.instType).toExchangeName(), it)
                    }

                    // handle funding changes
                    resp.data.forEach {
                        // Bitget sends accumulated funding, so we need to calculate funding delta between messages
                        // and spread it across the bot.
                        val symbolEx = SymbolEx(
                            ProductType.parse(resp.arg.instType).toExchangeName(),
                            it.symbol
                        )
                        // Reset funding for a symbol as it is a new position
                        if (it.totalFee == "") {
                            fundings[symbolEx] = 0.0
                            return@forEach
                        }
                        val funding = it.totalFee.toDouble()
                        // Prefill fundings when there is no value for a symbol.
                        // This happens when we restart our bot.
                        val change = funding - fundings.computeIfAbsent(symbolEx) { funding }
                        fundings[symbolEx] = funding
                        eventPublisher.publishAsync(
                            SettlementEvent(
                                symbolEx,
                                change,
                                it.marginCoin,
                                SettlementEvent.SettlementType.Funding
                            )
                        )
                    }
                }

                is LoginResponse -> {
                    log.info("Successfully authenticated to Bitget")
                }

                is ErrorResponse -> {
                    when (resp.code) {
                        ErrorResponse.ERR_USER_NEEDS_TO_LOGIN -> {
                            log.warn("Bitget socket authentication failed. Restarting socket")
                            socket.cancel()
                        }

                        // todo: send error to telegram [telegramSender.warnOnce]
                        else -> log.error("Unhandled error $text")
                    }
                }

                is SubscribeResponse -> {
                    when (resp.arg.channel) {
                        BitgetChannel.Fill.value -> {
                            log.info("Subscribed to trades for ${resp.arg.instType} ${resp.arg.instId}")
                        }

                        BitgetChannel.Orders.value -> {
                            log.info("Subscribed to orders for ${resp.arg.instType} ${resp.arg.instId}")
                        }

                        BitgetChannel.Positions.value -> {
                            log.info("Subscribed to positions for ${resp.arg.instType} ${resp.arg.instId}")
                        }

                        else -> {
                            log.info("Successfully subscribed")
                        }
                    }
                }

                else -> {
                    log.warn("Unsupported message type ${resp.javaClass}")
                }
            }
        } catch (e: IllegalArgumentException) {
            if (text == "pong") {
                return
            }
            throw e
        }
    }

    private fun Fill.toExExecution() =
        ExExecution(
            tradeId,
            orderId,
            SymbolEx(symbol.toBitgetExchange(), symbol),
            OrdStatus.Missing,
            baseVolume,
            price,
            ordersCache.get(orderId)?.botOrderId ?: "missing", // exOrderId can absent inside the map
            Instant.ofEpochMilli(cTime),
            feeDetail.sumOf { it.totalDeductionFee },
            feeDetail.first().feeCoin.toFeeAsset(),
            Side.parse(side)
        )

    private fun Fill.toExLiquidation() = ExLiquidation(
        tradeId,
        SymbolEx(symbol.toBitgetExchange(), symbol),
        baseVolume,
        Side.parse(side)
    )
}

@Serializable(with = BaseWebSocketResponseSerializer::class)
sealed class BaseWebSocketResponse

@OptIn(ExperimentalSerializationApi::class)
@Serializable
sealed class EventResponse : BaseWebSocketResponse() {
    abstract val event: String
}

@OptIn(ExperimentalSerializationApi::class)
@Serializable
sealed class SnapshotResponse : BaseWebSocketResponse() {
    abstract val action: String
    abstract val arg: BaseArg
}

@Serializable
@SerialName("login")
data class LoginResponse(
    override val event: String,
    val code: String
) : EventResponse()

@Serializable
@SerialName("error")
data class ErrorResponse(
    override val event: String,
    val code: Int,
    val msg: String
) : EventResponse() {
    companion object {
        const val ERR_USER_NEEDS_TO_LOGIN = 30004
    }
}

@Serializable
data class SubscriptionArg(
    val instType: String,
    val channel: String,
    val instId: String
)

@Serializable
@SerialName("subscribe")
data class SubscribeResponse(
    override val event: String,
    val arg: SubscriptionArg
) : EventResponse()

@Serializable
data class BaseArg(
    val instType: String,
    val channel: String,
    val instId: String
)

@Serializable
data class FeeDetail(
    val feeCoin: String,
    val deduction: String,
    val totalDeductionFee: Double,
    val totalFee: Double
)

@Serializable
@SerialName("fill")
data class FillResponse(
    override val action: String,
    override val arg: BaseArg,
    val data: List<Fill>,
) : SnapshotResponse()

@Serializable
data class Fill(
    val orderId: String,
    val tradeId: String,
    val symbol: String,
    val side: String,
    val price: Double,
    val baseVolume: Double,
    val tradeSide: String,
    val feeDetail: List<FeeDetail>,
    val cTime: Long // date of creation in milliseconds
)

@Serializable
@SerialName("orders")
data class OrdersResponse(
    override val action: String,
    override val arg: BaseArg,
    val data: List<SocketOrder>
) : SnapshotResponse()

/**
 * @param price is only filled for a limit order in statuses "live", "filled", "cancelled"
 * @param priceAvg is filled for a market and limit order when it is in "filled" status
 */
@Serializable
data class SocketOrder(
    @SerialName("instId") val symbol: String,
    @SerialName("orderId") val exOrderId: String,
    @SerialName("clientOid") val botOrderId: String,
    val size: Double,
    val price: Double,
    val status: String,
    val side: String,
    val orderType: String,
    // cumulative filled
    @SerialName("accBaseVolume") val filled: Double = 0.0,
    val priceAvg: Double = 0.0,
)

@Serializable
@SerialName("positions")
data class PositionsResponse(
    override val action: String,
    override val arg: BaseArg,
    val data: List<SocketPosition>
) : SnapshotResponse()

/**
 * @param totalFee is funding fee, the accumulated value of funding fee during
 * the position. The initial value is empty, indicating that no funding fee has
 * been charged yet.
 */
@Serializable
data class SocketPosition(
    @SerialName("instId") val symbol: String,
    @SerialName("total") val position: Double,
    @SerialName("holdSide") val side: String,
    val openPriceAvg: Double,
    val liquidationPrice: Double,
    val totalFee: String, // bitget sends us an empty string when no funding, and it breaks decoding, so we cast type in-place
    val marginCoin: String,
) {
    fun toExPosition() = ExPosition(
        SymbolEx(symbol.toBitgetExchange(), symbol),
        if (Side.parse(side) == Side.Buy) position else position.unaryMinus(),
        openPriceAvg,
        liquidationPrice
    )
}

@Serializable
@SerialName("ticker")
data class TickerResponse(
    override val action: String,
    override val arg: BaseArg,
    val data: List<Ticker>
) : SnapshotResponse()

@Serializable
data class Ticker(
    val instId: String,
    val fundingRate: Double,
    val nextFundingTime: Long
)

object BaseWebSocketResponseSerializer :
    JsonContentPolymorphicSerializer<BaseWebSocketResponse>(BaseWebSocketResponse::class) {
    override fun selectDeserializer(element: JsonElement): KSerializer<out BaseWebSocketResponse> {
        val json = element.jsonObject

        return when {
            json.containsKey("action") -> {
                when (json["arg"]?.jsonObject?.get("channel")?.jsonPrimitive?.content) {
                    BitgetChannel.Fill.value -> FillResponse.serializer()
                    BitgetChannel.Orders.value -> OrdersResponse.serializer()
                    BitgetChannel.Positions.value -> PositionsResponse.serializer()
                    BitgetChannel.Ticker.value -> TickerResponse.serializer()
                    else -> throw IllegalArgumentException("Unknown snapshot type")
                }
            }

            json.containsKey("event") -> {
                when (json["event"]?.jsonPrimitive?.content) {
                    "login" -> LoginResponse.serializer()
                    "error" -> ErrorResponse.serializer()
                    "subscribe" -> SubscribeResponse.serializer()
                    else -> throw IllegalArgumentException("Unknown event type")
                }
            }

            else -> throw IllegalArgumentException("Unknown message type")
        }
    }
}
