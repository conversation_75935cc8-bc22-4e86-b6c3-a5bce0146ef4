package trading.exchanges.bitget.socket

import io.micronaut.context.ApplicationContext
import io.micronaut.context.annotation.Context
import io.micronaut.context.annotation.Value
import io.micronaut.inject.qualifiers.Qualifiers
import io.micronaut.scheduling.TaskScheduler
import jakarta.annotation.PostConstruct
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import org.slf4j.LoggerFactory
import trading.events.EventPublisher
import trading.exchanges.bitget.BitgetConnector
import trading.exchanges.bitget.BitgetOrdersCache
import trading.exchanges.bitget.BitgetSigner
import trading.storage.repos.StrategyRepository
import trading.trading.holders.FundingHolder
import trading.trading.holders.PositionsHolder
import trading.utils.repeatDelayed
import kotlin.time.Duration.Companion.seconds

@Context
class BitgetSocketFactory(
    @Value("\${bitget.socket.address}") private val address: String?,
    @Value("\${bitget.socket.subscribers}") private val numberOfSubscriber: Int,
    @Value("\${bitget.keys.id}") private val apiKey: String?,
    @Value("\${bitget.keys.passphrase}") private val passphrase: String?,
    private val bitgetSigner: BitgetSigner,
    private val eventPublisher: EventPublisher,
    private val ordersCache: BitgetOrdersCache,
    private val positionsHolder: PositionsHolder,
    private val bitgetConnector: BitgetConnector,
    private val taskScheduler: TaskScheduler,
    private val strategyRepository: StrategyRepository,
    private val context: ApplicationContext,
    private val fundingHolder: FundingHolder,
) {
    private val log = LoggerFactory.getLogger(BitgetSocketFactory::class.java)

    companion object {
        private const val PRIVATE_SUBSCRIBER_NAME_TEMPLATE = "bitgetPrivateSocketSubscriber%d"
        private const val PRIVATE_LISTENER_NAME_TEMPLATE = "bitgetPrivateSocketListener%d"
        private const val PUBLIC_SUBSCRIBER_NAME = "bitgetPublicSocketSubscriber"
        private const val PUBLIC_LISTENER_NAME = "bitgetPublicSocketListener"
    }

    @PostConstruct
    fun initSubscribers() {
        if (apiKey.isNullOrEmpty()) {
            log.info("No Bitget key specified, skipping socket subscriptions")
            return
        }

        repeatDelayed(numberOfSubscriber, 1.seconds.inWholeMilliseconds) {
            registerPrivateSocket(it)
        }

        registerPublicSocket()
        initPositionsHolder()
    }

    private fun registerPrivateSocket(sn: Int) {
        val listener = BitgetPrivateSocketListener(eventPublisher, ordersCache, positionsHolder)
        context.registerSingleton(
            listener.javaClass,
            listener,
            Qualifiers.byName(
                String.format(PRIVATE_LISTENER_NAME_TEMPLATE, sn)
            )
        )

        context.registerSingleton(
            BitgetPrivateSocketSubscriber::class.java,
            BitgetPrivateSocketSubscriber(
                address,
                apiKey,
                passphrase,
                bitgetSigner,
                listener,
                taskScheduler,
                strategyRepository
            ),
            Qualifiers.byName(String.format(PRIVATE_SUBSCRIBER_NAME_TEMPLATE, sn))
        )
    }

    private fun registerPublicSocket() {
        val listener = BitgetPublicSocketListener(fundingHolder)
        context.registerSingleton(
            BitgetPublicSocketListener::class.java,
            listener,
            Qualifiers.byName(PUBLIC_LISTENER_NAME)
        )

        context.registerSingleton(
            BitgetPublicSocketSubscriber::class.java,
            BitgetPublicSocketSubscriber(address, listener, taskScheduler, strategyRepository, apiKey),
            Qualifiers.byName(PUBLIC_SUBSCRIBER_NAME)
        )
    }

    private fun initPositionsHolder() {
        runCatching {
            runBlocking(Dispatchers.IO) {
                "bitget-usdt".let { exchange ->
                    positionsHolder.store(
                        exchange,
                        bitgetConnector.getPositions(strategyRepository.findSymbolsByExchange(exchange))
                    )
                }
            }
        }.onFailure { ex: Throwable ->
            log.warn("Failed to init positions: ${ex.message}")
        }
    }
}